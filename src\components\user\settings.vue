<template>
  <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>系统设置</h2>
        <button class="close-button" @click="closeModal">&times;</button>
      </div>
      <div class="modal-body">
        <div class="tabs">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            :class="['tab-button', { active: activeTab === tab.id }]"
            @click="activeTab = tab.id"
          >
            {{ tab.label }}
          </button>
        </div>

        <div class="tab-content">
          <div v-if="activeTab === 'general'">
            <div class="setting-item">
              <label>语言</label>
              <div class="select-mock">
                <span>跟随系统</span>
                <span class="arrow">▼</span>
              </div>
            </div>
            <div class="setting-item">
              <label>主题</label>
              <div class="select-mock">
                <span>跟随系统</span>
                <span class="arrow">▼</span>
              </div>
            </div>
          </div>

          <div v-if="activeTab === 'account'">
            <div class="setting-item">
              <label>手机号码</label>
              <span>182********50</span>
            </div>
            <div class="setting-item-column">
              <div class="setting-item">
                <label>数据用于优化体验</label>
                <label class="switch">
                  <input type="checkbox" v-model="optimizeExperience" />
                  <span class="slider round"></span>
                </label>
              </div>
              <p class="description">
                允许我们将您的对话内容用于优化 DeepSeek 的使用体验。我们保障您的数据隐私安全。
              </p>
            </div>
            <div class="setting-item-column">
               <div class="setting-item">
                <label>导出所有历史对话</label>
                <button class="action-button">导出</button>
              </div>
              <p class="description">
                导出内容中将包含你的账号信息和所有历史对话。导出可能需要一段时间，下载链接的有效期为7天。
              </p>
            </div>
             <div class="setting-item">
              <label>登出所有设备</label>
              <button class="action-button">登出</button>
            </div>
            <div class="setting-item">
              <label>删除所有对话</label>
              <button class="action-button danger">删除</button>
            </div>
            <div class="setting-item">
              <label>注销账号</label>
              <button class="action-button danger">注销</button>
            </div>
          </div>

          <div v-if="activeTab === 'agreement'">
            <div class="setting-item">
              <label>用户协议</label>
              <button class="action-button">查看</button>
            </div>
            <div class="setting-item">
              <label>隐私政策</label>
              <button class="action-button">查看</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Tab {
  id: string;
  label: string;
}

// Props definition for v-model compatibility
defineProps<{
  modelValue: boolean;
}>();

// Emits definition for v-model compatibility
const emit = defineEmits(['update:modelValue']);

const activeTab = ref<string>('general'); // 默认选中的标签页
const optimizeExperience = ref<boolean>(true); // 数据优化体验开关的默认状态

const tabs: Tab[] = [
  { id: 'general', label: '通用设置' },
  { id: 'account', label: '账号管理' },
  { id: 'agreement', label: '服务协议' },
];

const closeModal = () => {
  emit('update:modelValue', false);
};
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 560px; // 根据图片估算宽度
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;

  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #888;
    padding: 0;
    line-height: 1;
    &:hover {
      color: #555;
    }
  }
}

.modal-body {
  padding: 0;
  overflow-y: auto;
}

.tabs {
  display: flex;
  background-color: #f7f7f7; // 标签栏背景色
  padding: 8px 24px 0 24px; // 调整内边距，使下边框与内容区对齐
  border-bottom: 1px solid #e0e0e0; // 添加分割线
  position: relative; // 用于 active 标签的下边框效果
  top: -1px; // 轻微上移，使得active tab的白色背景能覆盖父元素的边框

  .tab-button {
    padding: 10px 16px;
    margin-right: 4px; // 标签之间的间距
    border: none;
    background-color: transparent; // 未激活时透明
    color: #666;
    cursor: pointer;
    font-size: 14px;
    border-radius: 6px 6px 0 0; // 上方圆角
    position: relative; // 为了伪元素定位

    &.active {
      background-color: #fff; // 激活时白色背景
      color: #333;
      font-weight: 500;
      border-top: 1px solid #e0e0e0;
      border-left: 1px solid #e0e0e0;
      border-right: 1px solid #e0e0e0;
      border-bottom: 1px solid #fff; // 白色边框与内容区融合
      z-index: 1; // 确保激活标签在上方
      margin-bottom: -1px; // 覆盖父元素的下边框
    }

    &:not(.active):hover {
      background-color: #efefef;
      color: #333;
    }
  }
}

.tab-content {
  padding: 24px;
  min-height: 300px; // 给内容区一个最小高度
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;

  &:last-child {
    border-bottom: none;
  }

  label {
    color: #333;
    flex-shrink: 0; // 防止标签文字被压缩
    margin-right: 16px;
  }

  span:not(.arrow):not(.slider) { // 避免影响到 select-mock 和 switch 里的 span
    color: #666;
  }
}

.setting-item-column {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
   &:last-child {
    border-bottom: none;
  }
  .setting-item {
    border-bottom: none;
    padding-bottom: 8px;
  }
  .description {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
    margin-left: 0; // 与 setting-item 的 label 对齐（如果需要）
    padding-right: 70px; // 为按钮留出空间
  }
}


.select-mock {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  font-size: 14px;
  min-width: 120px; // 给一个最小宽度
  justify-content: space-between;

  .arrow {
    margin-left: 8px;
    font-size: 12px;
  }
}

.action-button {
  padding: 6px 16px;
  font-size: 14px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  &.danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #fff;

    &:hover {
      background-color: #f89898;
      border-color: #f89898;
    }
  }
}

// Switch Toggle
.switch {
  position: relative;
  display: inline-block;
  width: 44px; // 宽度调整
  height: 24px; // 高度调整
  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px; // 圆角调整

  &:before {
    position: absolute;
    content: "";
    height: 18px; // 滑块大小调整
    width: 18px;  // 滑块大小调整
    left: 3px;    // 位置调整
    bottom: 3px;  // 位置调整
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }
}

input:checked + .slider {
  background-color: #1890ff; // 激活颜色
}

input:focus + .slider {
  box-shadow: 0 0 1px #1890ff;
}

input:checked + .slider:before {
  transform: translateX(20px); // 滑动距离调整
}
</style>